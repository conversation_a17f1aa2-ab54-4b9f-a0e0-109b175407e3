"""
Sports data API collector
Collects player and team statistics from SportsDataIO and API-Football
"""
import requests
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import time
from config import Config
from database.db_manager import DatabaseManager

class SportsDataCollector:
    """Collects sports statistics from various APIs"""
    
    def __init__(self):
        self.sportsdata_key = Config.SPORTSDATA_API_KEY
        self.football_key = Config.API_FOOTBALL_KEY
        self.sportsdata_base = Config.SPORTSDATA_BASE_URL
        self.football_base = Config.API_FOOTBALL_BASE_URL
        self.db_manager = DatabaseManager()
    
    def get_mlb_stats(self, season: str = None) -> Dict[str, Any]:
        """Get MLB statistics from SportsDataIO"""
        if season is None:
            season = str(datetime.now().year)
            
        headers = {'Ocp-Apim-Subscription-Key': self.sportsdata_key}
        
        # Get team stats
        team_url = f"{self.sportsdata_base}/mlb/scores/json/TeamSeasonStats/{season}"
        player_url = f"{self.sportsdata_base}/mlb/scores/json/PlayerSeasonStats/{season}"
        
        try:
            # Team stats
            team_response = requests.get(team_url, headers=headers)
            team_response.raise_for_status()
            team_stats = team_response.json()
            
            # Player stats
            player_response = requests.get(player_url, headers=headers)
            player_response.raise_for_status()
            player_stats = player_response.json()
            
            return {
                'team_stats': team_stats,
                'player_stats': player_stats,
                'sport': 'mlb',
                'season': season
            }
            
        except requests.RequestException as e:
            print(f"Error fetching MLB stats: {e}")
            return {}
    
    def get_nba_stats(self, season: str = None) -> Dict[str, Any]:
        """Get NBA statistics from SportsDataIO"""
        if season is None:
            # NBA season format: 2024 for 2023-24 season
            season = str(datetime.now().year)
            
        headers = {'Ocp-Apim-Subscription-Key': self.sportsdata_key}
        
        team_url = f"{self.sportsdata_base}/nba/scores/json/TeamSeasonStats/{season}"
        player_url = f"{self.sportsdata_base}/nba/scores/json/PlayerSeasonStats/{season}"
        
        try:
            team_response = requests.get(team_url, headers=headers)
            team_response.raise_for_status()
            team_stats = team_response.json()
            
            player_response = requests.get(player_url, headers=headers)
            player_response.raise_for_status()
            player_stats = player_response.json()
            
            return {
                'team_stats': team_stats,
                'player_stats': player_stats,
                'sport': 'nba',
                'season': season
            }
            
        except requests.RequestException as e:
            print(f"Error fetching NBA stats: {e}")
            return {}
    
    def get_nfl_stats(self, season: str = None) -> Dict[str, Any]:
        """Get NFL statistics from SportsDataIO"""
        if season is None:
            season = str(datetime.now().year)
            
        headers = {'Ocp-Apim-Subscription-Key': self.sportsdata_key}
        
        team_url = f"{self.sportsdata_base}/nfl/scores/json/TeamSeasonStats/{season}"
        player_url = f"{self.sportsdata_base}/nfl/scores/json/PlayerSeasonStats/{season}"
        
        try:
            team_response = requests.get(team_url, headers=headers)
            team_response.raise_for_status()
            team_stats = team_response.json()
            
            player_response = requests.get(player_url, headers=headers)
            player_response.raise_for_status()
            player_stats = player_response.json()
            
            return {
                'team_stats': team_stats,
                'player_stats': player_stats,
                'sport': 'nfl',
                'season': season
            }
            
        except requests.RequestException as e:
            print(f"Error fetching NFL stats: {e}")
            return {}
    
    def get_soccer_stats(self, league_id: int = 39, season: int = None) -> Dict[str, Any]:
        """Get soccer statistics from API-Football"""
        if season is None:
            season = datetime.now().year
            
        headers = {
            'X-RapidAPI-Key': self.football_key,
            'X-RapidAPI-Host': 'v3.football.api-sports.io'
        }
        
        # Get team stats
        team_url = f"{self.football_base}/teams/statistics"
        params = {'league': league_id, 'season': season}
        
        try:
            # Get all teams in league first
            teams_url = f"{self.football_base}/teams"
            teams_params = {'league': league_id, 'season': season}
            
            teams_response = requests.get(teams_url, headers=headers, params=teams_params)
            teams_response.raise_for_status()
            teams_data = teams_response.json()
            
            all_team_stats = []
            all_player_stats = []
            
            for team in teams_data.get('response', []):
                team_id = team['team']['id']
                
                # Get team statistics
                team_stats_params = {'league': league_id, 'season': season, 'team': team_id}
                team_stats_response = requests.get(team_url, headers=headers, params=team_stats_params)
                
                if team_stats_response.status_code == 200:
                    team_stats = team_stats_response.json()
                    all_team_stats.extend(team_stats.get('response', []))
                
                # Get player statistics for team
                players_url = f"{self.football_base}/players"
                players_params = {'league': league_id, 'season': season, 'team': team_id}
                
                players_response = requests.get(players_url, headers=headers, params=players_params)
                if players_response.status_code == 200:
                    players_data = players_response.json()
                    all_player_stats.extend(players_data.get('response', []))
                
                # Rate limiting
                time.sleep(0.5)
            
            return {
                'team_stats': all_team_stats,
                'player_stats': all_player_stats,
                'sport': 'soccer',
                'league_id': league_id,
                'season': season
            }
            
        except requests.RequestException as e:
            print(f"Error fetching soccer stats: {e}")
            return {}
    
    def process_and_store_stats(self, stats_data: Dict[str, Any]):
        """Process and store statistics in database"""
        sport = stats_data.get('sport')
        season = stats_data.get('season')
        
        if not sport or not season:
            print("Missing sport or season information")
            return
        
        # Process team stats
        team_stats = stats_data.get('team_stats', [])
        for team_stat in team_stats:
            if sport == 'soccer':
                self._process_soccer_team_stats(team_stat, season)
            else:
                self._process_us_sports_team_stats(team_stat, sport, season)
        
        # Process player stats
        player_stats = stats_data.get('player_stats', [])
        for player_stat in player_stats:
            if sport == 'soccer':
                self._process_soccer_player_stats(player_stat, season)
            else:
                self._process_us_sports_player_stats(player_stat, sport, season)
    
    def _process_us_sports_team_stats(self, team_stat: Dict, sport: str, season: str):
        """Process US sports team statistics"""
        # This will vary by sport - implement specific logic for each
        team_name = team_stat.get('Team', team_stat.get('Name', ''))
        
        # Extract key stats based on sport
        if sport == 'mlb':
            stats_to_extract = ['Wins', 'Losses', 'RunsScored', 'RunsAllowed', 'BattingAverage']
        elif sport == 'nba':
            stats_to_extract = ['Wins', 'Losses', 'PointsPerGame', 'OpponentPointsPerGame']
        elif sport == 'nfl':
            stats_to_extract = ['Wins', 'Losses', 'PointsFor', 'PointsAgainst']
        else:
            stats_to_extract = []
        
        # Store each stat
        for stat_name in stats_to_extract:
            if stat_name in team_stat:
                # Store in database (implement based on your schema)
                pass
    
    def _process_soccer_team_stats(self, team_stat: Dict, season: int):
        """Process soccer team statistics"""
        # Implement soccer-specific team stat processing
        pass
    
    def _process_us_sports_player_stats(self, player_stat: Dict, sport: str, season: str):
        """Process US sports player statistics"""
        # Implement player stat processing
        pass
    
    def _process_soccer_player_stats(self, player_stat: Dict, season: int):
        """Process soccer player statistics"""
        # Implement soccer player stat processing
        pass
    
    def collect_all_sports_data(self) -> Dict[str, bool]:
        """Collect data for all supported sports"""
        results = {}
        
        # MLB
        print("Collecting MLB data...")
        mlb_data = self.get_mlb_stats()
        if mlb_data:
            self.process_and_store_stats(mlb_data)
            results['mlb'] = True
        else:
            results['mlb'] = False
        
        # NBA
        print("Collecting NBA data...")
        nba_data = self.get_nba_stats()
        if nba_data:
            self.process_and_store_stats(nba_data)
            results['nba'] = True
        else:
            results['nba'] = False
        
        # NFL
        print("Collecting NFL data...")
        nfl_data = self.get_nfl_stats()
        if nfl_data:
            self.process_and_store_stats(nfl_data)
            results['nfl'] = True
        else:
            results['nfl'] = False
        
        # Soccer (Premier League)
        print("Collecting Soccer data...")
        soccer_data = self.get_soccer_stats()
        if soccer_data:
            self.process_and_store_stats(soccer_data)
            results['soccer'] = True
        else:
            results['soccer'] = False
        
        return results
