"""
Database manager for the Sports Betting AI System
Handles SQLite database operations for storing odds, stats, and predictions
"""
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import json
from config import Config

class DatabaseManager:
    """Manages all database operations for the sports betting system"""
    
    def __init__(self, db_path: str = None):
        self.db_path = db_path or Config.DATABASE_PATH
        self.init_database()
    
    def init_database(self):
        """Initialize the database with required tables"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # Odds table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS odds (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sport TEXT NOT NULL,
                    game_id TEXT NOT NULL,
                    home_team TEXT NOT NULL,
                    away_team TEXT NOT NULL,
                    bookmaker TEXT NOT NULL,
                    market_type TEXT NOT NULL,
                    outcome TEXT NOT NULL,
                    odds REAL NOT NULL,
                    timestamp DATETIME NOT NULL,
                    game_date DATETIME NOT NULL,
                    UNIQUE(game_id, bookmaker, market_type, outcome, timestamp)
                )
            ''')
            
            # Games table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS games (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sport TEXT NOT NULL,
                    game_id TEXT UNIQUE NOT NULL,
                    home_team TEXT NOT NULL,
                    away_team TEXT NOT NULL,
                    game_date DATETIME NOT NULL,
                    status TEXT DEFAULT 'scheduled',
                    home_score INTEGER,
                    away_score INTEGER,
                    season TEXT,
                    week INTEGER,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Player stats table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS player_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sport TEXT NOT NULL,
                    player_id TEXT NOT NULL,
                    player_name TEXT NOT NULL,
                    team TEXT NOT NULL,
                    game_id TEXT,
                    stat_type TEXT NOT NULL,
                    stat_value REAL NOT NULL,
                    game_date DATETIME,
                    season TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Team stats table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS team_stats (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    sport TEXT NOT NULL,
                    team_name TEXT NOT NULL,
                    stat_type TEXT NOT NULL,
                    stat_value REAL NOT NULL,
                    game_date DATETIME,
                    season TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # Predictions table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS predictions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    game_id TEXT NOT NULL,
                    sport TEXT NOT NULL,
                    model_name TEXT NOT NULL,
                    prediction_type TEXT NOT NULL,
                    predicted_value REAL NOT NULL,
                    confidence REAL NOT NULL,
                    actual_value REAL,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    game_date DATETIME NOT NULL
                )
            ''')
            
            # Bets table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS bets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    game_id TEXT NOT NULL,
                    sport TEXT NOT NULL,
                    bet_type TEXT NOT NULL,
                    bookmaker TEXT NOT NULL,
                    odds REAL NOT NULL,
                    stake REAL NOT NULL,
                    potential_payout REAL NOT NULL,
                    status TEXT DEFAULT 'pending',
                    result TEXT,
                    profit_loss REAL,
                    placed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    game_date DATETIME NOT NULL
                )
            ''')
            
            conn.commit()
            print("Database initialized successfully")
    
    def insert_odds(self, odds_data: List[Dict[str, Any]]):
        """Insert odds data into the database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            for odds in odds_data:
                try:
                    cursor.execute('''
                        INSERT OR REPLACE INTO odds 
                        (sport, game_id, home_team, away_team, bookmaker, 
                         market_type, outcome, odds, timestamp, game_date)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        odds['sport'], odds['game_id'], odds['home_team'],
                        odds['away_team'], odds['bookmaker'], odds['market_type'],
                        odds['outcome'], odds['odds'], odds['timestamp'], odds['game_date']
                    ))
                except sqlite3.IntegrityError:
                    continue  # Skip duplicates
            
            conn.commit()
    
    def insert_game(self, game_data: Dict[str, Any]):
        """Insert game data into the database"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            cursor.execute('''
                INSERT OR REPLACE INTO games 
                (sport, game_id, home_team, away_team, game_date, status, 
                 home_score, away_score, season, week)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                game_data['sport'], game_data['game_id'], game_data['home_team'],
                game_data['away_team'], game_data['game_date'], game_data.get('status', 'scheduled'),
                game_data.get('home_score'), game_data.get('away_score'),
                game_data.get('season'), game_data.get('week')
            ))
            
            conn.commit()
    
    def get_latest_odds(self, sport: str, hours_back: int = 24) -> pd.DataFrame:
        """Get latest odds for a sport within specified hours"""
        with sqlite3.connect(self.db_path) as conn:
            cutoff_time = datetime.now() - timedelta(hours=hours_back)
            
            query = '''
                SELECT * FROM odds 
                WHERE sport = ? AND timestamp >= ?
                ORDER BY timestamp DESC
            '''
            
            return pd.read_sql_query(query, conn, params=(sport, cutoff_time))
    
    def get_upcoming_games(self, sport: str, days_ahead: int = 7) -> pd.DataFrame:
        """Get upcoming games for a sport"""
        with sqlite3.connect(self.db_path) as conn:
            end_date = datetime.now() + timedelta(days=days_ahead)
            
            query = '''
                SELECT * FROM games 
                WHERE sport = ? AND game_date BETWEEN ? AND ?
                ORDER BY game_date ASC
            '''
            
            return pd.read_sql_query(query, conn, params=(sport, datetime.now(), end_date))
    
    def close(self):
        """Close database connection"""
        pass  # Using context managers, so no explicit close needed
