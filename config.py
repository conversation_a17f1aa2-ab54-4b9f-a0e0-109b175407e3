"""
Configuration management for the Sports Betting AI System
"""
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for API keys and settings"""
    
    # API Keys (set these in your .env file)
    ODDS_API_KEY = os.getenv('ODDS_API_KEY', '')
    SPORTSDATA_API_KEY = os.getenv('SPORTSDATA_API_KEY', '')
    API_FOOTBALL_KEY = os.getenv('API_FOOTBALL_KEY', '')
    
    # API URLs
    ODDS_API_BASE_URL = "https://api.the-odds-api.com/v4"
    SPORTSDATA_BASE_URL = "https://api.sportsdata.io/v3"
    API_FOOTBALL_BASE_URL = "https://v3.football.api-sports.io"
    
    # Database settings
    DATABASE_PATH = "sports_betting.db"
    
    # Sports configuration
    SUPPORTED_SPORTS = {
        'mlb': {
            'odds_api_key': 'baseball_mlb',
            'sportsdata_endpoint': 'mlb',
            'priority': 1
        },
        'nba': {
            'odds_api_key': 'basketball_nba',
            'sportsdata_endpoint': 'nba',
            'priority': 2
        },
        'nfl': {
            'odds_api_key': 'americanfootball_nfl',
            'sportsdata_endpoint': 'nfl',
            'priority': 3
        },
        'soccer': {
            'odds_api_key': 'soccer_epl',  # Premier League
            'api_football_league': 39,  # Premier League ID
            'priority': 4
        }
    }
    
    # Betting settings
    DEFAULT_BANKROLL = 1000.0
    MIN_VALUE_THRESHOLD = 0.05  # 5% minimum value bet threshold
    MAX_BET_PERCENTAGE = 0.02   # Max 2% of bankroll per bet
    
    # Data collection settings
    UPDATE_FREQUENCY_MINUTES = 30
    HISTORICAL_DAYS = 30
    
    # ML Model settings
    TRAIN_TEST_SPLIT = 0.8
    RANDOM_STATE = 42
    
    @classmethod
    def validate_config(cls):
        """Validate that required API keys are present"""
        missing_keys = []
        
        if not cls.ODDS_API_KEY:
            missing_keys.append('ODDS_API_KEY')
        if not cls.SPORTSDATA_API_KEY:
            missing_keys.append('SPORTSDATA_API_KEY')
        if not cls.API_FOOTBALL_KEY:
            missing_keys.append('API_FOOTBALL_KEY')
            
        if missing_keys:
            print(f"Warning: Missing API keys: {', '.join(missing_keys)}")
            print("Please add them to your .env file")
            
        return len(missing_keys) == 0
