"""
Test script to verify the Sports Betting AI System is working correctly
Run this after installation to check all components
"""
import sys
import traceback
from datetime import datetime

def test_imports():
    """Test that all required modules can be imported"""
    print("🔍 Testing imports...")
    
    try:
        import pandas as pd
        print("✅ pandas imported successfully")
    except ImportError as e:
        print(f"❌ pandas import failed: {e}")
        return False
    
    try:
        import numpy as np
        print("✅ numpy imported successfully")
    except ImportError as e:
        print(f"❌ numpy import failed: {e}")
        return False
    
    try:
        import requests
        print("✅ requests imported successfully")
    except ImportError as e:
        print(f"❌ requests import failed: {e}")
        return False
    
    try:
        from sklearn.ensemble import RandomForestClassifier
        print("✅ scikit-learn imported successfully")
    except ImportError as e:
        print(f"❌ scikit-learn import failed: {e}")
        return False
    
    try:
        import click
        print("✅ click imported successfully")
    except ImportError as e:
        print(f"❌ click import failed: {e}")
        return False
    
    try:
        from rich.console import Console
        print("✅ rich imported successfully")
    except ImportError as e:
        print(f"❌ rich import failed: {e}")
        return False
    
    return True

def test_config():
    """Test configuration loading"""
    print("\n🔧 Testing configuration...")
    
    try:
        from config import Config
        print("✅ Config module imported successfully")
        
        # Test configuration validation
        Config.validate_config()
        print("✅ Configuration validation completed")
        
        # Check supported sports
        if Config.SUPPORTED_SPORTS:
            print(f"✅ Found {len(Config.SUPPORTED_SPORTS)} supported sports")
        else:
            print("❌ No supported sports configured")
            return False
            
        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        traceback.print_exc()
        return False

def test_database():
    """Test database initialization"""
    print("\n🗄️ Testing database...")
    
    try:
        from database.db_manager import DatabaseManager
        print("✅ DatabaseManager imported successfully")
        
        # Initialize database
        db_manager = DatabaseManager()
        print("✅ Database initialized successfully")
        
        # Test basic operations
        from datetime import datetime
        test_odds = [{
            'sport': 'test',
            'game_id': 'test_game_1',
            'home_team': 'Test Home',
            'away_team': 'Test Away',
            'bookmaker': 'test_book',
            'market_type': 'h2h',
            'outcome': 'Test Home',
            'odds': 2.0,
            'timestamp': datetime.now(),
            'game_date': datetime.now()
        }]
        
        db_manager.insert_odds(test_odds)
        print("✅ Database insert test successful")
        
        return True
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        traceback.print_exc()
        return False

def test_odds_scraper():
    """Test odds scraper initialization"""
    print("\n📊 Testing odds scraper...")
    
    try:
        from data_collector.odds_scraper import OddsScraper
        print("✅ OddsScraper imported successfully")
        
        scraper = OddsScraper()
        print("✅ OddsScraper initialized successfully")
        
        # Test sports list (this will fail without API key, but should not crash)
        try:
            sports = scraper.get_sports()
            if sports:
                print(f"✅ API connection working - found {len(sports)} sports")
            else:
                print("⚠️ No sports data (likely missing API key)")
        except Exception as e:
            print(f"⚠️ API call failed (expected without API key): {str(e)[:100]}...")
        
        return True
    except Exception as e:
        print(f"❌ Odds scraper test failed: {e}")
        traceback.print_exc()
        return False

def test_sports_api():
    """Test sports API collector"""
    print("\n🏈 Testing sports API collector...")
    
    try:
        from data_collector.sports_api import SportsDataCollector
        print("✅ SportsDataCollector imported successfully")
        
        collector = SportsDataCollector()
        print("✅ SportsDataCollector initialized successfully")
        
        return True
    except Exception as e:
        print(f"❌ Sports API test failed: {e}")
        traceback.print_exc()
        return False

def test_trend_analyzer():
    """Test trend analyzer"""
    print("\n📈 Testing trend analyzer...")
    
    try:
        from analysis.trend_analyzer import TrendAnalyzer
        print("✅ TrendAnalyzer imported successfully")
        
        analyzer = TrendAnalyzer()
        print("✅ TrendAnalyzer initialized successfully")
        
        return True
    except Exception as e:
        print(f"❌ Trend analyzer test failed: {e}")
        traceback.print_exc()
        return False

def test_prediction_engine():
    """Test prediction engine"""
    print("\n🤖 Testing prediction engine...")
    
    try:
        from models.prediction_engine import PredictionEngine
        print("✅ PredictionEngine imported successfully")
        
        engine = PredictionEngine()
        print("✅ PredictionEngine initialized successfully")
        
        return True
    except Exception as e:
        print(f"❌ Prediction engine test failed: {e}")
        traceback.print_exc()
        return False

def test_utilities():
    """Test utility functions"""
    print("\n🛠️ Testing utilities...")
    
    try:
        from utils.helpers import calculate_implied_probability, kelly_criterion
        print("✅ Utility functions imported successfully")
        
        # Test some utility functions
        prob = calculate_implied_probability(2.0, 'decimal')
        if prob == 0.5:
            print("✅ Implied probability calculation working")
        else:
            print(f"⚠️ Implied probability calculation unexpected result: {prob}")
        
        kelly = kelly_criterion(0.6, 2.0, 1000)
        if kelly >= 0:
            print("✅ Kelly criterion calculation working")
        else:
            print(f"⚠️ Kelly criterion calculation unexpected result: {kelly}")
        
        return True
    except Exception as e:
        print(f"❌ Utilities test failed: {e}")
        traceback.print_exc()
        return False

def test_main_cli():
    """Test main CLI interface"""
    print("\n🖥️ Testing CLI interface...")
    
    try:
        import main
        print("✅ Main CLI module imported successfully")
        return True
    except Exception as e:
        print(f"❌ CLI test failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Sports Betting AI System - Component Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_config),
        ("Database", test_database),
        ("Odds Scraper", test_odds_scraper),
        ("Sports API", test_sports_api),
        ("Trend Analyzer", test_trend_analyzer),
        ("Prediction Engine", test_prediction_engine),
        ("Utilities", test_utilities),
        ("CLI Interface", test_main_cli),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Your system is ready to use.")
        print("\nNext steps:")
        print("1. Get your API keys from the services mentioned in README.md")
        print("2. Add them to your .env file")
        print("3. Run: python main.py setup")
        print("4. Start collecting data: python main.py collect-odds --sport mlb")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")
        print("Make sure all dependencies are installed: pip install -r requirements.txt")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
