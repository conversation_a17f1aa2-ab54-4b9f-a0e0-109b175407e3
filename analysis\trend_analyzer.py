"""
Trend analyzer for sports betting
Analyzes historical data to identify betting trends and patterns
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
from database.db_manager import DatabaseManager
import matplotlib.pyplot as plt
import seaborn as sns

class TrendAnalyzer:
    """Analyzes sports betting trends and patterns"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
    
    def analyze_team_performance_trends(self, sport: str, team: str, 
                                      days_back: int = 30) -> Dict[str, Any]:
        """Analyze team performance trends over specified period"""
        # Get team's recent games
        with self.db_manager.db_path as db_path:
            import sqlite3
            conn = sqlite3.connect(db_path)
            
            cutoff_date = datetime.now() - timedelta(days=days_back)
            
            query = '''
                SELECT * FROM games 
                WHERE sport = ? AND (home_team = ? OR away_team = ?) 
                AND game_date >= ? AND status = 'completed'
                ORDER BY game_date DESC
            '''
            
            games_df = pd.read_sql_query(
                query, conn, 
                params=(sport, team, team, cutoff_date)
            )
            
            conn.close()
        
        if games_df.empty:
            return {'error': f'No recent games found for {team}'}
        
        # Calculate trends
        trends = {
            'team': team,
            'sport': sport,
            'games_analyzed': len(games_df),
            'date_range': f"{cutoff_date.strftime('%Y-%m-%d')} to {datetime.now().strftime('%Y-%m-%d')}"
        }
        
        # Win/Loss record
        wins = 0
        losses = 0
        
        for _, game in games_df.iterrows():
            if game['home_team'] == team:
                if game['home_score'] > game['away_score']:
                    wins += 1
                else:
                    losses += 1
            else:  # away team
                if game['away_score'] > game['home_score']:
                    wins += 1
                else:
                    losses += 1
        
        trends['record'] = {'wins': wins, 'losses': losses, 'win_percentage': wins / (wins + losses) if (wins + losses) > 0 else 0}
        
        # Home vs Away performance
        home_games = games_df[games_df['home_team'] == team]
        away_games = games_df[games_df['away_team'] == team]
        
        trends['home_record'] = self._calculate_record(home_games, team, True)
        trends['away_record'] = self._calculate_record(away_games, team, False)
        
        # Scoring trends
        trends['scoring_trends'] = self._analyze_scoring_trends(games_df, team)
        
        # Recent form (last 5 games)
        recent_games = games_df.head(5)
        trends['recent_form'] = self._calculate_record(recent_games, team)
        
        return trends
    
    def analyze_over_under_trends(self, sport: str, team: str = None, 
                                 days_back: int = 30) -> Dict[str, Any]:
        """Analyze over/under betting trends"""
        with self.db_manager.db_path as db_path:
            import sqlite3
            conn = sqlite3.connect(db_path)
            
            cutoff_date = datetime.now() - timedelta(days=days_back)
            
            if team:
                query = '''
                    SELECT g.*, o.odds, o.outcome, o.bookmaker
                    FROM games g
                    JOIN odds o ON g.game_id = o.game_id
                    WHERE g.sport = ? AND (g.home_team = ? OR g.away_team = ?)
                    AND g.game_date >= ? AND g.status = 'completed'
                    AND o.market_type = 'totals'
                    ORDER BY g.game_date DESC
                '''
                params = (sport, team, team, cutoff_date)
            else:
                query = '''
                    SELECT g.*, o.odds, o.outcome, o.bookmaker
                    FROM games g
                    JOIN odds o ON g.game_id = o.game_id
                    WHERE g.sport = ? AND g.game_date >= ? AND g.status = 'completed'
                    AND o.market_type = 'totals'
                    ORDER BY g.game_date DESC
                '''
                params = (sport, cutoff_date)
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
        
        if df.empty:
            return {'error': 'No over/under data found'}
        
        # Analyze over/under results
        over_count = 0
        under_count = 0
        
        for game_id in df['game_id'].unique():
            game_data = df[df['game_id'] == game_id].iloc[0]
            total_score = game_data['home_score'] + game_data['away_score']
            
            # Get the total line (this would need to be extracted from odds data)
            # For now, we'll use a placeholder calculation
            over_count += 1 if total_score > 200 else 0  # Placeholder
            under_count += 1 if total_score <= 200 else 0  # Placeholder
        
        return {
            'sport': sport,
            'team': team,
            'over_count': over_count,
            'under_count': under_count,
            'over_percentage': over_count / (over_count + under_count) if (over_count + under_count) > 0 else 0,
            'games_analyzed': len(df['game_id'].unique())
        }
    
    def find_value_betting_opportunities(self, sport: str, 
                                       min_value_threshold: float = 0.05) -> List[Dict[str, Any]]:
        """Find current value betting opportunities"""
        # Get recent odds
        odds_df = self.db_manager.get_latest_odds(sport, hours_back=6)
        
        if odds_df.empty:
            return []
        
        value_bets = []
        
        # Group by game and market
        for (game_id, market_type), group in odds_df.groupby(['game_id', 'market_type']):
            if market_type == 'h2h':  # Head-to-head markets
                outcomes = group['outcome'].unique()
                
                for outcome in outcomes:
                    outcome_odds = group[group['outcome'] == outcome]['odds']
                    
                    if len(outcome_odds) >= 2:  # Need multiple bookmakers
                        max_odds = outcome_odds.max()
                        avg_odds = outcome_odds.mean()
                        
                        # Calculate implied probability and value
                        implied_prob = 1 / avg_odds
                        value = (max_odds * implied_prob) - 1
                        
                        if value >= min_value_threshold:
                            game_info = group.iloc[0]
                            best_book_row = group[group['odds'] == max_odds].iloc[0]
                            
                            value_bet = {
                                'game_id': game_id,
                                'sport': sport,
                                'home_team': game_info['home_team'],
                                'away_team': game_info['away_team'],
                                'outcome': outcome,
                                'best_odds': max_odds,
                                'avg_odds': avg_odds,
                                'value': value,
                                'bookmaker': best_book_row['bookmaker'],
                                'game_date': game_info['game_date'],
                                'market_type': market_type
                            }
                            
                            value_bets.append(value_bet)
        
        # Sort by value descending
        return sorted(value_bets, key=lambda x: x['value'], reverse=True)
    
    def analyze_betting_patterns(self, sport: str, pattern_type: str, 
                               **kwargs) -> Dict[str, Any]:
        """Analyze specific betting patterns based on user criteria"""
        
        if pattern_type == 'home_favorites':
            return self._analyze_home_favorites(sport, **kwargs)
        elif pattern_type == 'road_underdogs':
            return self._analyze_road_underdogs(sport, **kwargs)
        elif pattern_type == 'divisional_games':
            return self._analyze_divisional_games(sport, **kwargs)
        elif pattern_type == 'back_to_back':
            return self._analyze_back_to_back_games(sport, **kwargs)
        elif pattern_type == 'high_total_games':
            return self._analyze_high_total_games(sport, **kwargs)
        else:
            return {'error': f'Unknown pattern type: {pattern_type}'}
    
    def _calculate_record(self, games_df: pd.DataFrame, team: str, 
                         is_home: bool = None) -> Dict[str, Any]:
        """Calculate win/loss record for a team"""
        if games_df.empty:
            return {'wins': 0, 'losses': 0, 'win_percentage': 0}
        
        wins = 0
        losses = 0
        
        for _, game in games_df.iterrows():
            if is_home is None:  # Calculate for both home and away
                if game['home_team'] == team:
                    wins += 1 if game['home_score'] > game['away_score'] else 0
                    losses += 1 if game['home_score'] <= game['away_score'] else 0
                else:
                    wins += 1 if game['away_score'] > game['home_score'] else 0
                    losses += 1 if game['away_score'] <= game['home_score'] else 0
            elif is_home:  # Home games only
                wins += 1 if game['home_score'] > game['away_score'] else 0
                losses += 1 if game['home_score'] <= game['away_score'] else 0
            else:  # Away games only
                wins += 1 if game['away_score'] > game['home_score'] else 0
                losses += 1 if game['away_score'] <= game['home_score'] else 0
        
        total_games = wins + losses
        win_percentage = wins / total_games if total_games > 0 else 0
        
        return {
            'wins': wins,
            'losses': losses,
            'win_percentage': win_percentage,
            'total_games': total_games
        }
    
    def _analyze_scoring_trends(self, games_df: pd.DataFrame, team: str) -> Dict[str, Any]:
        """Analyze scoring trends for a team"""
        scores = []
        opponent_scores = []
        
        for _, game in games_df.iterrows():
            if game['home_team'] == team:
                scores.append(game['home_score'])
                opponent_scores.append(game['away_score'])
            else:
                scores.append(game['away_score'])
                opponent_scores.append(game['home_score'])
        
        if not scores:
            return {}
        
        return {
            'avg_points_scored': np.mean(scores),
            'avg_points_allowed': np.mean(opponent_scores),
            'scoring_variance': np.var(scores),
            'highest_score': max(scores),
            'lowest_score': min(scores)
        }
    
    def _analyze_home_favorites(self, sport: str, **kwargs) -> Dict[str, Any]:
        """Analyze performance of home favorites"""
        # Implementation for home favorites analysis
        return {'pattern': 'home_favorites', 'analysis': 'placeholder'}
    
    def _analyze_road_underdogs(self, sport: str, **kwargs) -> Dict[str, Any]:
        """Analyze performance of road underdogs"""
        return {'pattern': 'road_underdogs', 'analysis': 'placeholder'}
    
    def _analyze_divisional_games(self, sport: str, **kwargs) -> Dict[str, Any]:
        """Analyze divisional game patterns"""
        return {'pattern': 'divisional_games', 'analysis': 'placeholder'}
    
    def _analyze_back_to_back_games(self, sport: str, **kwargs) -> Dict[str, Any]:
        """Analyze back-to-back game patterns"""
        return {'pattern': 'back_to_back', 'analysis': 'placeholder'}
    
    def _analyze_high_total_games(self, sport: str, **kwargs) -> Dict[str, Any]:
        """Analyze high total games patterns"""
        return {'pattern': 'high_total_games', 'analysis': 'placeholder'}
