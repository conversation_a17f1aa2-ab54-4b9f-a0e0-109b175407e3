# Getting Started with Your Sports Betting AI System

## 🎉 System Successfully Installed!

Your sports betting algorithm and AI prediction system is now ready to use. Here's how to get started:

## 📋 What You Have

✅ **Complete Sports Betting AI System** with:
- Multi-sport support (MLB, NBA, NFL, Soccer)
- Real-time odds collection from major sportsbooks
- Statistical analysis and trend detection
- Value betting identification
- Machine learning prediction models
- Local SQLite database for data storage

## 🚀 Quick Start Guide

### Step 1: Get Your API Keys (Required)

You'll need free API keys from these services:

1. **The Odds API** (for betting odds)
   - Go to: https://the-odds-api.com/
   - Sign up for free account (500 requests/month)
   - Get your API key

2. **SportsDataIO** (for MLB/NBA/NFL stats)
   - Go to: https://sportsdata.io/
   - Sign up for free trial
   - Get your API key

3. **API-Football** (for soccer stats)
   - Go to: https://www.api-football.com/
   - Sign up for free account (100 requests/day)
   - Get your API key

### Step 2: Configure Your API Keys

1. Copy `.env.example` to `.env`:
   ```bash
   copy .env.example .env
   ```

2. Edit `.env` file and add your API keys:
   ```
   ODDS_API_KEY=your_actual_odds_api_key_here
   SPORTSDATA_API_KEY=your_actual_sportsdata_key_here
   API_FOOTBALL_KEY=your_actual_api_football_key_here
   ```

### Step 3: Test the System

```bash
# Check system status
python main.py setup

# View current status
python main.py status
```

## 🎯 How to Use Your Betting Algorithm

### Collect Current Odds
```bash
# Get odds for all sports
python main.py collect-odds --sport all

# Get odds for specific sport (MLB is your priority #1)
python main.py collect-odds --sport mlb
```

### Collect Statistics
```bash
# Get stats for all sports
python main.py collect-stats --sport all

# Get stats for MLB only
python main.py collect-stats --sport mlb
```

### Find Value Bets
```bash
# Find MLB value opportunities (5% minimum edge)
python main.py find-value --sport mlb --min-value 0.05

# Find NBA value bets with 3% minimum edge
python main.py find-value --sport nba --min-value 0.03
```

### Analyze Trends
```bash
# Analyze specific team trends
python main.py analyze-trends --sport mlb --team "New York Yankees" --days 30

# Analyze general sport trends
python main.py analyze-trends --sport nba
```

### Pattern Analysis
```bash
# Analyze home favorites in NFL
python main.py analyze-pattern --sport nfl --pattern home_favorites

# Check road underdogs in MLB
python main.py analyze-pattern --sport mlb --pattern road_underdogs
```

## 📊 Your Workflow

### Daily Routine:
1. **Morning**: Collect fresh odds and stats
   ```bash
   python main.py collect-odds --sport mlb
   python main.py collect-stats --sport mlb
   ```

2. **Find Opportunities**: Look for value bets
   ```bash
   python main.py find-value --sport mlb --min-value 0.05
   ```

3. **Analyze Trends**: Check team performance
   ```bash
   python main.py analyze-trends --sport mlb --team "Team Name"
   ```

### Weekly Analysis:
```bash
# Check patterns across different scenarios
python main.py analyze-pattern --sport mlb --pattern home_favorites
python main.py analyze-pattern --sport mlb --pattern road_underdogs
```

## 🎲 Understanding the Output

### Value Bets Table:
- **Game**: Teams playing
- **Outcome**: What you're betting on
- **Best Odds**: Highest odds available
- **Avg Odds**: Average across bookmakers
- **Value**: Your edge percentage
- **Bookmaker**: Where to place the bet

### Trend Analysis:
- **Win Percentage**: Team's recent performance
- **Home/Away Records**: Location-based performance
- **Scoring Trends**: Offensive/defensive metrics
- **Recent Form**: Last 5 games performance

## ⚠️ Important Notes

### Responsible Betting:
- This is a tool for analysis, not guaranteed profits
- Never bet more than you can afford to lose
- Always verify odds before placing bets
- Consider this educational/research software

### API Limits:
- **The Odds API**: 500 requests/month (free)
- **SportsDataIO**: Varies by plan
- **API-Football**: 100 requests/day (free)

### Data Storage:
- All data stored locally in `sports_betting.db`
- No data sent to external servers
- You own all your historical data

## 🔧 Troubleshooting

### Common Issues:

1. **"No API key" errors**:
   - Check your `.env` file exists
   - Verify API keys are correct
   - Make sure no extra spaces in keys

2. **"No data found" errors**:
   - Run data collection first
   - Check API rate limits
   - Verify internet connection

3. **Database errors**:
   - Delete `sports_betting.db` and run setup again

### Getting Help:
```bash
# See all available commands
python main.py --help

# Get help for specific command
python main.py find-value --help
```

## 🚀 Next Steps

1. **Get your API keys** and configure the system
2. **Start with MLB** (your priority sport)
3. **Collect data daily** for best results
4. **Analyze trends** before making decisions
5. **Track your performance** over time

## 📈 Advanced Features

Once you're comfortable with the basics:
- Train custom ML models for predictions
- Set up automated data collection
- Create custom betting strategies
- Build performance tracking

---

**Remember**: This system helps you make informed decisions, but sports betting always involves risk. Use responsibly and within your means!

Happy betting! 🎯
