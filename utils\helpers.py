"""
Utility functions for the Sports Betting AI System
"""
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import re

def calculate_implied_probability(odds: float, odds_format: str = 'decimal') -> float:
    """Calculate implied probability from odds"""
    if odds_format == 'decimal':
        return 1 / odds
    elif odds_format == 'american':
        if odds > 0:
            return 100 / (odds + 100)
        else:
            return abs(odds) / (abs(odds) + 100)
    else:
        raise ValueError("Unsupported odds format")

def calculate_value(true_probability: float, odds: float, odds_format: str = 'decimal') -> float:
    """Calculate betting value (Kelly Criterion basis)"""
    implied_prob = calculate_implied_probability(odds, odds_format)
    return (true_probability * odds) - 1 if true_probability > implied_prob else 0

def kelly_criterion(win_probability: float, odds: float, bankroll: float) -> float:
    """Calculate optimal bet size using Kelly Criterion"""
    b = odds - 1  # Net odds received on the wager
    p = win_probability  # Probability of winning
    q = 1 - p  # Probability of losing
    
    if p * b > q:
        fraction = (p * b - q) / b
        return min(fraction * bankroll, bankroll * 0.05)  # Cap at 5% of bankroll
    else:
        return 0  # No bet recommended

def normalize_team_name(team_name: str) -> str:
    """Normalize team names for consistent matching"""
    # Remove common prefixes/suffixes and standardize
    team_name = team_name.strip()
    
    # Common replacements
    replacements = {
        'LA Lakers': 'Los Angeles Lakers',
        'LA Clippers': 'Los Angeles Clippers',
        'NY Yankees': 'New York Yankees',
        'NY Mets': 'New York Mets',
        'SF Giants': 'San Francisco Giants',
    }
    
    return replacements.get(team_name, team_name)

def calculate_elo_rating(team_rating: float, opponent_rating: float, 
                        actual_score: int, opponent_score: int, k_factor: int = 32) -> float:
    """Calculate new Elo rating after a game"""
    # Expected score
    expected = 1 / (1 + 10**((opponent_rating - team_rating) / 400))
    
    # Actual score (1 for win, 0.5 for tie, 0 for loss)
    if actual_score > opponent_score:
        actual = 1
    elif actual_score == opponent_score:
        actual = 0.5
    else:
        actual = 0
    
    # New rating
    new_rating = team_rating + k_factor * (actual - expected)
    return new_rating

def moving_average(data: List[float], window: int) -> List[float]:
    """Calculate moving average"""
    if len(data) < window:
        return data
    
    return [sum(data[i:i+window])/window for i in range(len(data)-window+1)]

def calculate_team_strength(wins: int, losses: int, points_for: float, 
                          points_against: float) -> float:
    """Calculate a composite team strength metric"""
    if wins + losses == 0:
        return 0.5
    
    win_pct = wins / (wins + losses)
    point_diff = points_for - points_against
    
    # Normalize point differential (assuming average game score around 100-200 points)
    normalized_diff = point_diff / (wins + losses) / 100
    
    # Combine win percentage (70%) and point differential (30%)
    strength = (0.7 * win_pct) + (0.3 * (0.5 + normalized_diff))
    
    # Clamp between 0 and 1
    return max(0, min(1, strength))

def detect_betting_patterns(odds_history: pd.DataFrame) -> Dict[str, Any]:
    """Detect patterns in betting odds movements"""
    patterns = {}
    
    if odds_history.empty:
        return patterns
    
    # Group by game and outcome
    for (game_id, outcome), group in odds_history.groupby(['game_id', 'outcome']):
        if len(group) < 3:  # Need at least 3 data points
            continue
        
        odds_series = group.sort_values('timestamp')['odds']
        
        # Calculate trend
        if len(odds_series) >= 2:
            trend = 'rising' if odds_series.iloc[-1] > odds_series.iloc[0] else 'falling'
            volatility = odds_series.std()
            
            patterns[f"{game_id}_{outcome}"] = {
                'trend': trend,
                'volatility': volatility,
                'current_odds': odds_series.iloc[-1],
                'starting_odds': odds_series.iloc[0],
                'change_pct': (odds_series.iloc[-1] - odds_series.iloc[0]) / odds_series.iloc[0]
            }
    
    return patterns

def format_currency(amount: float) -> str:
    """Format currency for display"""
    return f"${amount:,.2f}"

def format_percentage(value: float) -> str:
    """Format percentage for display"""
    return f"{value:.1%}"

def format_odds(odds: float, format_type: str = 'decimal') -> str:
    """Format odds for display"""
    if format_type == 'decimal':
        return f"{odds:.2f}"
    elif format_type == 'american':
        if odds >= 2.0:
            american = (odds - 1) * 100
            return f"+{american:.0f}"
        else:
            american = -100 / (odds - 1)
            return f"{american:.0f}"
    else:
        return str(odds)

def calculate_roi(bets: List[Dict[str, Any]]) -> Dict[str, float]:
    """Calculate return on investment for a series of bets"""
    if not bets:
        return {'roi': 0, 'total_staked': 0, 'total_return': 0, 'profit': 0}
    
    total_staked = sum(bet.get('stake', 0) for bet in bets)
    total_return = sum(bet.get('payout', 0) for bet in bets if bet.get('result') == 'win')
    profit = total_return - total_staked
    roi = (profit / total_staked) if total_staked > 0 else 0
    
    return {
        'roi': roi,
        'total_staked': total_staked,
        'total_return': total_return,
        'profit': profit
    }

def validate_bet_input(stake: float, odds: float, bankroll: float) -> Tuple[bool, str]:
    """Validate betting input parameters"""
    if stake <= 0:
        return False, "Stake must be positive"
    
    if odds <= 1.0:
        return False, "Odds must be greater than 1.0"
    
    if stake > bankroll:
        return False, "Stake cannot exceed bankroll"
    
    if stake > bankroll * 0.1:  # Warning for large bets
        return False, "Stake exceeds 10% of bankroll (recommended maximum)"
    
    return True, "Valid bet"

def parse_game_string(game_str: str) -> Tuple[str, str]:
    """Parse game string to extract team names"""
    # Common formats: "Team A @ Team B", "Team A vs Team B", "Team A - Team B"
    separators = [' @ ', ' vs ', ' - ', ' v ']
    
    for sep in separators:
        if sep in game_str:
            parts = game_str.split(sep)
            if len(parts) == 2:
                return parts[0].strip(), parts[1].strip()
    
    # If no separator found, return as is
    return game_str, ""

def get_sport_season(sport: str, date: datetime = None) -> str:
    """Get the current season for a sport"""
    if date is None:
        date = datetime.now()
    
    year = date.year
    month = date.month
    
    if sport == 'mlb':
        # MLB season runs April-October
        if month >= 4:
            return str(year)
        else:
            return str(year - 1)
    elif sport == 'nba':
        # NBA season runs October-June
        if month >= 10:
            return f"{year}-{str(year + 1)[2:]}"
        else:
            return f"{year - 1}-{str(year)[2:]}"
    elif sport == 'nfl':
        # NFL season runs September-February
        if month >= 9:
            return str(year)
        else:
            return str(year - 1)
    elif sport == 'soccer':
        # European season runs August-May
        if month >= 8:
            return f"{year}-{str(year + 1)[2:]}"
        else:
            return f"{year - 1}-{str(year)[2:]}"
    
    return str(year)

def clean_numeric_data(value: Any) -> Optional[float]:
    """Clean and convert numeric data"""
    if value is None or value == '':
        return None
    
    if isinstance(value, (int, float)):
        return float(value)
    
    if isinstance(value, str):
        # Remove common non-numeric characters
        cleaned = re.sub(r'[^\d.-]', '', value)
        try:
            return float(cleaned)
        except ValueError:
            return None
    
    return None
