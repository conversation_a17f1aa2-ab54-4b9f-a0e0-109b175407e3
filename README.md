# Sports Betting AI System

A comprehensive sports betting algorithm and AI prediction system that runs locally on your computer. This system collects odds from major sportsbooks, analyzes player/team statistics, identifies betting trends, and provides value betting recommendations.

## Features

- **Multi-Sport Support**: MLB (priority 1), NBA (priority 2), NFL (priority 3), Soccer (priority 4)
- **Real-time Odds Collection**: Scrapes odds from DraftKings, FanDuel, BetMGM, Caesars, and more
- **Statistical Analysis**: Collects player and team statistics from professional APIs
- **Trend Analysis**: Identifies betting patterns and trends based on historical data
- **Value Betting**: Finds opportunities where odds provide positive expected value
- **AI Predictions**: Machine learning models for game outcome predictions
- **Local Database**: SQLite database for storing all historical data locally

## Installation

1. **Clone or download this repository**
2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up API keys**:
   - Copy `.env.example` to `.env`
   - Get API keys from:
     - [The Odds API](https://the-odds-api.com/) - Free tier available
     - [SportsDataIO](https://sportsdata.io/) - Free tier available  
     - [API-Football](https://www.api-football.com/) - Free tier available
   - Add your keys to the `.env` file

4. **Initialize the system**:
   ```bash
   python main.py setup
   ```

## Quick Start

### 1. Check System Status
```bash
python main.py status
```

### 2. Collect Current Odds
```bash
# Collect odds for all sports
python main.py collect-odds --sport all

# Collect odds for specific sport
python main.py collect-odds --sport mlb
```

### 3. Collect Statistics
```bash
# Collect stats for all sports
python main.py collect-stats --sport all

# Collect stats for specific sport
python main.py collect-stats --sport nba
```

### 4. Find Value Bets
```bash
# Find value bets for MLB with 5% minimum value
python main.py find-value --sport mlb --min-value 0.05
```

### 5. Analyze Trends
```bash
# Analyze team-specific trends
python main.py analyze-trends --sport mlb --team "New York Yankees" --days 30

# Analyze general sport trends
python main.py analyze-trends --sport nba
```

### 6. Analyze Specific Patterns
```bash
# Analyze home favorites pattern
python main.py analyze-pattern --sport nfl --pattern home_favorites

# Analyze road underdogs pattern
python main.py analyze-pattern --sport mlb --pattern road_underdogs
```

## Usage Examples

### Finding Value Bets for Tonight's Games
```bash
# Get current MLB value opportunities
python main.py find-value --sport mlb --min-value 0.03

# Check NBA games with higher value threshold
python main.py find-value --sport nba --min-value 0.08
```

### Team Analysis
```bash
# Analyze Lakers recent performance
python main.py analyze-trends --sport nba --team "Los Angeles Lakers" --days 20

# Check Yankees trends over last month
python main.py analyze-trends --sport mlb --team "New York Yankees" --days 30
```

### Pattern Analysis
```bash
# Check how home favorites perform in NFL
python main.py analyze-pattern --sport nfl --pattern home_favorites

# Analyze high-scoring games in NBA
python main.py analyze-pattern --sport nba --pattern high_total_games
```

## System Architecture

```
├── config.py              # Configuration and API settings
├── main.py                 # Command-line interface
├── data_collector/         # Data collection modules
│   ├── odds_scraper.py     # Odds collection from sportsbooks
│   └── sports_api.py       # Player/team statistics collection
├── database/               # Database management
│   └── db_manager.py       # SQLite database operations
├── models/                 # Machine learning models
│   └── prediction_engine.py # AI prediction models
├── analysis/               # Analysis and trend detection
│   └── trend_analyzer.py   # Betting trend analysis
├── betting/                # Betting strategy (future)
├── utils/                  # Utility functions
│   └── helpers.py          # Helper functions and calculations
└── requirements.txt        # Python dependencies
```

## Supported Sports & APIs

### MLB (Priority 1)
- **Odds**: The Odds API (`baseball_mlb`)
- **Stats**: SportsDataIO MLB API
- **Season**: April - October

### NBA (Priority 2)  
- **Odds**: The Odds API (`basketball_nba`)
- **Stats**: SportsDataIO NBA API
- **Season**: October - June

### NFL (Priority 3)
- **Odds**: The Odds API (`americanfootball_nfl`) 
- **Stats**: SportsDataIO NFL API
- **Season**: September - February

### Soccer (Priority 4)
- **Odds**: The Odds API (`soccer_epl`)
- **Stats**: API-Football (Premier League)
- **Season**: August - May

## Database Schema

The system uses SQLite with the following main tables:
- `odds` - Betting odds from various sportsbooks
- `games` - Game information and results
- `player_stats` - Individual player statistics
- `team_stats` - Team performance metrics
- `predictions` - AI model predictions
- `bets` - Betting history and results

## Configuration

Key settings in `config.py`:
- `DEFAULT_BANKROLL`: Starting bankroll amount
- `MIN_VALUE_THRESHOLD`: Minimum value for bet recommendations
- `MAX_BET_PERCENTAGE`: Maximum bet size as % of bankroll
- `UPDATE_FREQUENCY_MINUTES`: How often to collect new data

## API Rate Limits

- **The Odds API**: 500 requests/month (free tier)
- **SportsDataIO**: Varies by endpoint (free tier available)
- **API-Football**: 100 requests/day (free tier)

## Future Enhancements

- [ ] Advanced ML models (neural networks, ensemble methods)
- [ ] Live betting integration
- [ ] Automated bet placement (with user approval)
- [ ] Web dashboard interface
- [ ] Mobile notifications for value bets
- [ ] Bankroll management automation
- [ ] Performance tracking and analytics

## Disclaimer

This software is for educational and research purposes only. Sports betting involves risk and you should never bet more than you can afford to lose. Always gamble responsibly and check your local laws regarding sports betting.

## Support

For issues or questions:
1. Check the system status: `python main.py status`
2. Verify your API keys in the `.env` file
3. Ensure you have sufficient API quota remaining
4. Check that your internet connection is stable

## License

This project is for personal use only. Please respect the terms of service of all APIs used.
