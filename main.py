"""
Main application for Sports Betting AI System
Command-line interface for interacting with the betting algorithm
"""
import click
import json
from datetime import datetime
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich import print as rprint

from config import Config
from data_collector.odds_scraper import OddsScraper
from data_collector.sports_api import SportsDataCollector
from analysis.trend_analyzer import TrendAnalyzer
from database.db_manager import DatabaseManager

console = Console()

@click.group()
def cli():
    """Sports Betting AI System - Your local betting algorithm assistant"""
    # Validate configuration on startup
    if not Config.validate_config():
        console.print("[red]Warning: Some API keys are missing. Check your .env file.[/red]")

@cli.command()
def setup():
    """Initial setup and configuration check"""
    console.print("[bold blue]Sports Betting AI System Setup[/bold blue]")
    
    # Check API keys
    console.print("\n[yellow]Checking API Configuration...[/yellow]")
    
    if Config.ODDS_API_KEY:
        console.print("✅ The Odds API key configured")
    else:
        console.print("❌ The Odds API key missing")
    
    if Config.SPORTSDATA_API_KEY:
        console.print("✅ SportsDataIO API key configured")
    else:
        console.print("❌ SportsDataIO API key missing")
    
    if Config.API_FOOTBALL_KEY:
        console.print("✅ API-Football key configured")
    else:
        console.print("❌ API-Football key missing")
    
    # Initialize database
    console.print("\n[yellow]Initializing Database...[/yellow]")
    db_manager = DatabaseManager()
    console.print("✅ Database initialized successfully")
    
    # Display supported sports
    console.print("\n[yellow]Supported Sports (in priority order):[/yellow]")
    for sport, config in sorted(Config.SUPPORTED_SPORTS.items(), key=lambda x: x[1]['priority']):
        console.print(f"  {config['priority']}. {sport.upper()}")

@cli.command()
@click.option('--sport', type=click.Choice(['mlb', 'nba', 'nfl', 'soccer', 'all']), default='all')
def collect_odds(sport):
    """Collect betting odds from sportsbooks"""
    console.print(f"[bold green]Collecting odds for {sport.upper()}...[/bold green]")
    
    scraper = OddsScraper()
    
    if sport == 'all':
        results = scraper.scrape_all_priority_sports()
        
        # Display results
        table = Table(title="Odds Collection Results")
        table.add_column("Sport", style="cyan")
        table.add_column("Status", style="green")
        
        for sport_name, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            table.add_row(sport_name.upper(), status)
        
        console.print(table)
    else:
        sport_config = Config.SUPPORTED_SPORTS.get(sport)
        if sport_config and 'odds_api_key' in sport_config:
            success = scraper.scrape_sport_odds(sport_config['odds_api_key'])
            status = "✅ Success" if success else "❌ Failed"
            console.print(f"Odds collection for {sport.upper()}: {status}")
        else:
            console.print(f"[red]Sport {sport} not supported for odds collection[/red]")

@cli.command()
@click.option('--sport', type=click.Choice(['mlb', 'nba', 'nfl', 'soccer', 'all']), default='all')
def collect_stats(sport):
    """Collect player and team statistics"""
    console.print(f"[bold green]Collecting statistics for {sport.upper()}...[/bold green]")
    
    collector = SportsDataCollector()
    
    if sport == 'all':
        results = collector.collect_all_sports_data()
        
        table = Table(title="Statistics Collection Results")
        table.add_column("Sport", style="cyan")
        table.add_column("Status", style="green")
        
        for sport_name, success in results.items():
            status = "✅ Success" if success else "❌ Failed"
            table.add_row(sport_name.upper(), status)
        
        console.print(table)
    else:
        # Individual sport collection
        if sport == 'mlb':
            data = collector.get_mlb_stats()
        elif sport == 'nba':
            data = collector.get_nba_stats()
        elif sport == 'nfl':
            data = collector.get_nfl_stats()
        elif sport == 'soccer':
            data = collector.get_soccer_stats()
        
        if data:
            collector.process_and_store_stats(data)
            console.print(f"✅ Statistics collected for {sport.upper()}")
        else:
            console.print(f"❌ Failed to collect statistics for {sport.upper()}")

@cli.command()
@click.option('--sport', type=click.Choice(['mlb', 'nba', 'nfl', 'soccer']), required=True)
@click.option('--min-value', default=0.05, help='Minimum value threshold (default: 0.05)')
def find_value(sport, min_value):
    """Find value betting opportunities"""
    console.print(f"[bold green]Finding value bets for {sport.upper()}...[/bold green]")
    
    scraper = OddsScraper()
    value_bets = scraper.find_value_bets(sport, min_value)
    
    if not value_bets:
        console.print(f"[yellow]No value bets found for {sport.upper()} with minimum value of {min_value}[/yellow]")
        return
    
    table = Table(title=f"Value Betting Opportunities - {sport.upper()}")
    table.add_column("Game", style="cyan")
    table.add_column("Outcome", style="white")
    table.add_column("Best Odds", style="green")
    table.add_column("Avg Odds", style="yellow")
    table.add_column("Value", style="bold green")
    table.add_column("Bookmaker", style="blue")
    
    for bet in value_bets[:10]:  # Show top 10
        game = f"{bet['away_team']} @ {bet['home_team']}"
        value_pct = f"{bet['value']:.1%}"
        table.add_row(
            game, bet['outcome'], f"{bet['best_odds']:.2f}",
            f"{bet['avg_odds']:.2f}", value_pct, bet['bookmaker']
        )
    
    console.print(table)

@cli.command()
@click.option('--sport', type=click.Choice(['mlb', 'nba', 'nfl', 'soccer']), required=True)
@click.option('--team', help='Specific team to analyze')
@click.option('--days', default=30, help='Days of history to analyze')
def analyze_trends(sport, team, days):
    """Analyze betting trends and patterns"""
    console.print(f"[bold green]Analyzing trends for {sport.upper()}...[/bold green]")
    
    analyzer = TrendAnalyzer()
    
    if team:
        # Team-specific analysis
        trends = analyzer.analyze_team_performance_trends(sport, team, days)
        
        if 'error' in trends:
            console.print(f"[red]{trends['error']}[/red]")
            return
        
        # Display team trends
        panel_content = f"""
[bold]{trends['team']}[/bold] - {trends['sport'].upper()}
Games Analyzed: {trends['games_analyzed']}
Date Range: {trends['date_range']}

[yellow]Overall Record:[/yellow]
Wins: {trends['record']['wins']} | Losses: {trends['record']['losses']} | Win %: {trends['record']['win_percentage']:.1%}

[yellow]Home Record:[/yellow]
Wins: {trends['home_record']['wins']} | Losses: {trends['home_record']['losses']} | Win %: {trends['home_record']['win_percentage']:.1%}

[yellow]Away Record:[/yellow]
Wins: {trends['away_record']['wins']} | Losses: {trends['away_record']['losses']} | Win %: {trends['away_record']['win_percentage']:.1%}

[yellow]Recent Form (Last 5):[/yellow]
Wins: {trends['recent_form']['wins']} | Losses: {trends['recent_form']['losses']} | Win %: {trends['recent_form']['win_percentage']:.1%}
        """
        
        console.print(Panel(panel_content, title=f"{team} Analysis", border_style="blue"))
    
    else:
        # General sport analysis
        value_bets = analyzer.find_value_betting_opportunities(sport)
        
        if value_bets:
            console.print(f"[green]Found {len(value_bets)} value betting opportunities[/green]")
            
            table = Table(title=f"Current Value Bets - {sport.upper()}")
            table.add_column("Game", style="cyan")
            table.add_column("Outcome", style="white")
            table.add_column("Value", style="bold green")
            table.add_column("Best Odds", style="green")
            table.add_column("Bookmaker", style="blue")
            
            for bet in value_bets[:5]:
                game = f"{bet['away_team']} @ {bet['home_team']}"
                value_pct = f"{bet['value']:.1%}"
                table.add_row(
                    game, bet['outcome'], value_pct,
                    f"{bet['best_odds']:.2f}", bet['bookmaker']
                )
            
            console.print(table)
        else:
            console.print(f"[yellow]No current value bets found for {sport.upper()}[/yellow]")

@cli.command()
@click.option('--sport', type=click.Choice(['mlb', 'nba', 'nfl', 'soccer']), required=True)
@click.option('--pattern', type=click.Choice(['home_favorites', 'road_underdogs', 'divisional_games', 'back_to_back', 'high_total_games']), required=True)
def analyze_pattern(sport, pattern):
    """Analyze specific betting patterns"""
    console.print(f"[bold green]Analyzing {pattern} pattern for {sport.upper()}...[/bold green]")
    
    analyzer = TrendAnalyzer()
    result = analyzer.analyze_betting_patterns(sport, pattern)
    
    console.print(json.dumps(result, indent=2))

@cli.command()
def status():
    """Show system status and recent data"""
    console.print("[bold blue]Sports Betting AI System Status[/bold blue]")
    
    db_manager = DatabaseManager()
    
    # Check recent data for each sport
    table = Table(title="Recent Data Status")
    table.add_column("Sport", style="cyan")
    table.add_column("Recent Odds", style="green")
    table.add_column("Upcoming Games", style="yellow")
    
    for sport in ['mlb', 'nba', 'nfl', 'soccer']:
        odds_df = db_manager.get_latest_odds(sport, hours_back=24)
        games_df = db_manager.get_upcoming_games(sport, days_ahead=7)
        
        odds_count = len(odds_df) if not odds_df.empty else 0
        games_count = len(games_df) if not games_df.empty else 0
        
        table.add_row(sport.upper(), str(odds_count), str(games_count))
    
    console.print(table)

if __name__ == '__main__':
    cli()
