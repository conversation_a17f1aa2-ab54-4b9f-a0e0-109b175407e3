"""
Odds scraper using The Odds API
Collects betting odds from major sportsbooks for MLB, NBA, NFL, and Soccer
"""
import requests
import json
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import time
from config import Config
from database.db_manager import DatabaseManager

class OddsScraper:
    """Scrapes betting odds from The Odds API"""
    
    def __init__(self):
        self.api_key = Config.ODDS_API_KEY
        self.base_url = Config.ODDS_API_BASE_URL
        self.db_manager = DatabaseManager()
        
        # Bookmakers to focus on
        self.preferred_bookmakers = [
            'draftkings', 'fanduel', 'betmgm', 'caesars', 
            'pointsbet', 'betrivers', 'unibet_us'
        ]
        
        # Markets to collect
        self.markets = ['h2h', 'spreads', 'totals']  # Head-to-head, spreads, over/under
    
    def get_sports(self) -> List[Dict[str, Any]]:
        """Get list of available sports from The Odds API"""
        url = f"{self.base_url}/sports"
        params = {'api_key': self.api_key}
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            return response.json()
        except requests.RequestException as e:
            print(f"Error fetching sports: {e}")
            return []
    
    def get_odds(self, sport: str, markets: List[str] = None, 
                 bookmakers: List[str] = None) -> List[Dict[str, Any]]:
        """Get odds for a specific sport"""
        if markets is None:
            markets = self.markets
        if bookmakers is None:
            bookmakers = self.preferred_bookmakers
            
        url = f"{self.base_url}/sports/{sport}/odds"
        params = {
            'api_key': self.api_key,
            'regions': 'us',  # US bookmakers
            'markets': ','.join(markets),
            'bookmakers': ','.join(bookmakers),
            'oddsFormat': 'decimal',
            'dateFormat': 'iso'
        }
        
        try:
            response = requests.get(url, params=params)
            response.raise_for_status()
            
            # Check remaining requests
            remaining = response.headers.get('x-requests-remaining')
            if remaining:
                print(f"API requests remaining: {remaining}")
            
            return response.json()
        except requests.RequestException as e:
            print(f"Error fetching odds for {sport}: {e}")
            return []
    
    def process_odds_data(self, raw_odds: List[Dict[str, Any]], sport: str) -> List[Dict[str, Any]]:
        """Process raw odds data into standardized format"""
        processed_odds = []
        timestamp = datetime.now()
        
        for game in raw_odds:
            game_id = game['id']
            home_team = game['home_team']
            away_team = game['away_team']
            game_date = datetime.fromisoformat(game['commence_time'].replace('Z', '+00:00'))
            
            for bookmaker in game.get('bookmakers', []):
                bookmaker_name = bookmaker['key']
                
                for market in bookmaker.get('markets', []):
                    market_type = market['key']
                    
                    for outcome in market.get('outcomes', []):
                        odds_entry = {
                            'sport': sport,
                            'game_id': game_id,
                            'home_team': home_team,
                            'away_team': away_team,
                            'bookmaker': bookmaker_name,
                            'market_type': market_type,
                            'outcome': outcome['name'],
                            'odds': outcome['price'],
                            'timestamp': timestamp,
                            'game_date': game_date
                        }
                        
                        # Add point/line for spreads and totals
                        if 'point' in outcome:
                            odds_entry['point'] = outcome['point']
                            
                        processed_odds.append(odds_entry)
        
        return processed_odds
    
    def scrape_sport_odds(self, sport_key: str) -> bool:
        """Scrape odds for a specific sport and save to database"""
        print(f"Scraping odds for {sport_key}...")
        
        raw_odds = self.get_odds(sport_key)
        if not raw_odds:
            print(f"No odds data retrieved for {sport_key}")
            return False
        
        processed_odds = self.process_odds_data(raw_odds, sport_key)
        
        if processed_odds:
            self.db_manager.insert_odds(processed_odds)
            print(f"Saved {len(processed_odds)} odds entries for {sport_key}")
            return True
        else:
            print(f"No processed odds for {sport_key}")
            return False
    
    def scrape_all_priority_sports(self) -> Dict[str, bool]:
        """Scrape odds for all priority sports"""
        results = {}
        
        # Sort sports by priority
        sorted_sports = sorted(
            Config.SUPPORTED_SPORTS.items(), 
            key=lambda x: x[1]['priority']
        )
        
        for sport_name, sport_config in sorted_sports:
            if 'odds_api_key' in sport_config:
                sport_key = sport_config['odds_api_key']
                results[sport_name] = self.scrape_sport_odds(sport_key)
                
                # Rate limiting - wait between requests
                time.sleep(1)
        
        return results
    
    def get_best_odds(self, sport: str, market_type: str = 'h2h') -> Dict[str, Any]:
        """Get best available odds for each outcome in recent games"""
        df = self.db_manager.get_latest_odds(sport, hours_back=6)
        
        if df.empty:
            return {}
        
        # Filter by market type
        df = df[df['market_type'] == market_type]
        
        # Group by game and outcome, get best odds
        best_odds = df.groupby(['game_id', 'outcome'])['odds'].max().reset_index()
        
        # Convert to dictionary format
        result = {}
        for _, row in best_odds.iterrows():
            game_id = row['game_id']
            if game_id not in result:
                result[game_id] = {}
            result[game_id][row['outcome']] = row['odds']
        
        return result
    
    def find_value_bets(self, sport: str, min_value: float = 0.05) -> List[Dict[str, Any]]:
        """Find potential value bets based on odds discrepancies"""
        df = self.db_manager.get_latest_odds(sport, hours_back=2)
        
        if df.empty:
            return []
        
        value_bets = []
        
        # Group by game and outcome
        for (game_id, outcome), group in df.groupby(['game_id', 'outcome']):
            if len(group) < 2:  # Need at least 2 bookmakers for comparison
                continue
            
            max_odds = group['odds'].max()
            avg_odds = group['odds'].mean()
            
            # Calculate implied probability and value
            implied_prob = 1 / avg_odds
            value = (max_odds * implied_prob) - 1
            
            if value >= min_value:
                best_book = group[group['odds'] == max_odds].iloc[0]
                
                value_bet = {
                    'game_id': game_id,
                    'sport': sport,
                    'outcome': outcome,
                    'best_odds': max_odds,
                    'avg_odds': avg_odds,
                    'value': value,
                    'bookmaker': best_book['bookmaker'],
                    'home_team': best_book['home_team'],
                    'away_team': best_book['away_team'],
                    'game_date': best_book['game_date']
                }
                
                value_bets.append(value_bet)
        
        # Sort by value descending
        return sorted(value_bets, key=lambda x: x['value'], reverse=True)
