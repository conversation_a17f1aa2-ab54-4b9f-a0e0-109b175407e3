"""
Prediction engine for sports betting
Uses machine learning models to predict game outcomes and betting values
"""
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier, GradientBoostingRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import accuracy_score, classification_report
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import joblib
import os

from database.db_manager import DatabaseManager
from utils.helpers import calculate_team_strength, get_sport_season

class PredictionEngine:
    """Machine learning prediction engine for sports betting"""
    
    def __init__(self):
        self.db_manager = DatabaseManager()
        self.models = {}
        self.scalers = {}
        self.encoders = {}
        self.model_dir = "models/saved_models"
        
        # Create model directory if it doesn't exist
        os.makedirs(self.model_dir, exist_ok=True)
    
    def prepare_training_data(self, sport: str, days_back: int = 365) -> pd.DataFrame:
        """Prepare training data for a specific sport"""
        cutoff_date = datetime.now() - timedelta(days=days_back)
        
        # Get completed games with odds
        with self.db_manager.db_path as db_path:
            import sqlite3
            conn = sqlite3.connect(db_path)
            
            query = '''
                SELECT g.*, o.odds, o.outcome, o.bookmaker, o.market_type
                FROM games g
                JOIN odds o ON g.game_id = o.game_id
                WHERE g.sport = ? AND g.game_date >= ? AND g.status = 'completed'
                AND o.market_type = 'h2h'
                ORDER BY g.game_date DESC
            '''
            
            df = pd.read_sql_query(query, conn, params=(sport, cutoff_date))
            conn.close()
        
        if df.empty:
            return pd.DataFrame()
        
        # Process and engineer features
        processed_df = self._engineer_features(df, sport)
        return processed_df
    
    def _engineer_features(self, df: pd.DataFrame, sport: str) -> pd.DataFrame:
        """Engineer features for machine learning"""
        features = []
        
        for game_id in df['game_id'].unique():
            game_data = df[df['game_id'] == game_id].iloc[0]
            
            # Basic game features
            feature_row = {
                'game_id': game_id,
                'sport': sport,
                'home_team': game_data['home_team'],
                'away_team': game_data['away_team'],
                'game_date': game_data['game_date'],
                'home_score': game_data['home_score'],
                'away_score': game_data['away_score'],
                'season': game_data.get('season', get_sport_season(sport, 
                    datetime.fromisoformat(game_data['game_date']))),
            }
            
            # Calculate outcome
            if game_data['home_score'] > game_data['away_score']:
                feature_row['winner'] = 'home'
                feature_row['home_win'] = 1
            else:
                feature_row['winner'] = 'away'
                feature_row['home_win'] = 0
            
            # Score features
            feature_row['total_score'] = game_data['home_score'] + game_data['away_score']
            feature_row['score_diff'] = abs(game_data['home_score'] - game_data['away_score'])
            
            # Odds features (average across bookmakers)
            game_odds = df[df['game_id'] == game_id]
            
            home_odds = game_odds[game_odds['outcome'] == game_data['home_team']]['odds']
            away_odds = game_odds[game_odds['outcome'] == game_data['away_team']]['odds']
            
            if not home_odds.empty:
                feature_row['home_odds_avg'] = home_odds.mean()
                feature_row['home_implied_prob'] = (1 / home_odds.mean())
            
            if not away_odds.empty:
                feature_row['away_odds_avg'] = away_odds.mean()
                feature_row['away_implied_prob'] = (1 / away_odds.mean())
            
            # Market efficiency features
            if not home_odds.empty and not away_odds.empty:
                total_implied = feature_row.get('home_implied_prob', 0) + feature_row.get('away_implied_prob', 0)
                feature_row['market_efficiency'] = total_implied  # Should be close to 1 for efficient markets
            
            features.append(feature_row)
        
        feature_df = pd.DataFrame(features)
        
        # Add historical team performance features
        feature_df = self._add_team_performance_features(feature_df, sport)
        
        return feature_df
    
    def _add_team_performance_features(self, df: pd.DataFrame, sport: str) -> pd.DataFrame:
        """Add team performance features based on historical data"""
        # This is a simplified version - in practice, you'd calculate rolling averages
        # of team performance metrics over recent games
        
        for idx, row in df.iterrows():
            game_date = datetime.fromisoformat(row['game_date'])
            
            # Get recent performance for both teams (last 10 games before this game)
            home_recent = self._get_recent_team_performance(
                row['home_team'], sport, game_date, games_back=10
            )
            away_recent = self._get_recent_team_performance(
                row['away_team'], sport, game_date, games_back=10
            )
            
            # Add features
            df.at[idx, 'home_recent_wins'] = home_recent.get('wins', 0)
            df.at[idx, 'home_recent_losses'] = home_recent.get('losses', 0)
            df.at[idx, 'home_recent_win_pct'] = home_recent.get('win_pct', 0.5)
            df.at[idx, 'home_avg_score'] = home_recent.get('avg_score', 0)
            df.at[idx, 'home_avg_allowed'] = home_recent.get('avg_allowed', 0)
            
            df.at[idx, 'away_recent_wins'] = away_recent.get('wins', 0)
            df.at[idx, 'away_recent_losses'] = away_recent.get('losses', 0)
            df.at[idx, 'away_recent_win_pct'] = away_recent.get('win_pct', 0.5)
            df.at[idx, 'away_avg_score'] = away_recent.get('avg_score', 0)
            df.at[idx, 'away_avg_allowed'] = away_recent.get('avg_allowed', 0)
            
            # Relative strength
            df.at[idx, 'win_pct_diff'] = home_recent.get('win_pct', 0.5) - away_recent.get('win_pct', 0.5)
            df.at[idx, 'scoring_diff'] = home_recent.get('avg_score', 0) - away_recent.get('avg_score', 0)
        
        return df
    
    def _get_recent_team_performance(self, team: str, sport: str, 
                                   before_date: datetime, games_back: int = 10) -> Dict[str, float]:
        """Get recent team performance metrics"""
        # Simplified implementation - would query database for recent games
        # For now, return default values
        return {
            'wins': 5,
            'losses': 5,
            'win_pct': 0.5,
            'avg_score': 100,
            'avg_allowed': 100
        }
    
    def train_model(self, sport: str, model_type: str = 'outcome') -> Dict[str, Any]:
        """Train prediction model for a sport"""
        print(f"Training {model_type} model for {sport}...")
        
        # Prepare training data
        df = self.prepare_training_data(sport)
        
        if df.empty:
            return {'error': 'No training data available'}
        
        # Select features and target
        feature_columns = [
            'home_implied_prob', 'away_implied_prob', 'market_efficiency',
            'home_recent_win_pct', 'away_recent_win_pct', 'win_pct_diff',
            'home_avg_score', 'away_avg_score', 'scoring_diff'
        ]
        
        # Remove rows with missing features
        df_clean = df.dropna(subset=feature_columns + ['home_win'])
        
        if len(df_clean) < 50:  # Need minimum data for training
            return {'error': 'Insufficient training data'}
        
        X = df_clean[feature_columns]
        
        if model_type == 'outcome':
            y = df_clean['home_win']
            model = RandomForestClassifier(n_estimators=100, random_state=42)
        elif model_type == 'total':
            y = df_clean['total_score']
            model = GradientBoostingRegressor(n_estimators=100, random_state=42)
        else:
            return {'error': f'Unknown model type: {model_type}'}
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42
        )
        
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Train model
        model.fit(X_train_scaled, y_train)
        
        # Evaluate
        if model_type == 'outcome':
            train_score = model.score(X_train_scaled, y_train)
            test_score = model.score(X_test_scaled, y_test)
            predictions = model.predict(X_test_scaled)
            accuracy = accuracy_score(y_test, predictions)
            
            results = {
                'model_type': model_type,
                'sport': sport,
                'train_accuracy': train_score,
                'test_accuracy': test_score,
                'accuracy': accuracy,
                'training_samples': len(X_train),
                'test_samples': len(X_test)
            }
        else:
            train_score = model.score(X_train_scaled, y_train)
            test_score = model.score(X_test_scaled, y_test)
            
            results = {
                'model_type': model_type,
                'sport': sport,
                'train_r2': train_score,
                'test_r2': test_score,
                'training_samples': len(X_train),
                'test_samples': len(X_test)
            }
        
        # Save model
        model_key = f"{sport}_{model_type}"
        self.models[model_key] = model
        self.scalers[model_key] = scaler
        
        # Save to disk
        model_path = os.path.join(self.model_dir, f"{model_key}_model.joblib")
        scaler_path = os.path.join(self.model_dir, f"{model_key}_scaler.joblib")
        
        joblib.dump(model, model_path)
        joblib.dump(scaler, scaler_path)
        
        print(f"Model saved: {model_path}")
        
        return results
    
    def predict_game(self, sport: str, home_team: str, away_team: str, 
                    game_date: datetime = None) -> Dict[str, Any]:
        """Predict outcome for a specific game"""
        if game_date is None:
            game_date = datetime.now()
        
        model_key = f"{sport}_outcome"
        
        if model_key not in self.models:
            # Try to load from disk
            model_path = os.path.join(self.model_dir, f"{model_key}_model.joblib")
            scaler_path = os.path.join(self.model_dir, f"{model_key}_scaler.joblib")
            
            if os.path.exists(model_path) and os.path.exists(scaler_path):
                self.models[model_key] = joblib.load(model_path)
                self.scalers[model_key] = joblib.load(scaler_path)
            else:
                return {'error': f'No trained model found for {sport}'}
        
        # Get current odds for the game
        odds_df = self.db_manager.get_latest_odds(sport, hours_back=24)
        
        # Find game odds (simplified - would need better game matching)
        game_odds = odds_df[
            (odds_df['home_team'] == home_team) & 
            (odds_df['away_team'] == away_team)
        ]
        
        if game_odds.empty:
            return {'error': 'No current odds found for this game'}
        
        # Calculate features (simplified)
        home_odds = game_odds[game_odds['outcome'] == home_team]['odds'].mean()
        away_odds = game_odds[game_odds['outcome'] == away_team]['odds'].mean()
        
        if pd.isna(home_odds) or pd.isna(away_odds):
            return {'error': 'Incomplete odds data'}
        
        # Get recent team performance
        home_recent = self._get_recent_team_performance(home_team, sport, game_date)
        away_recent = self._get_recent_team_performance(away_team, sport, game_date)
        
        # Create feature vector
        features = np.array([[
            1/home_odds,  # home_implied_prob
            1/away_odds,  # away_implied_prob
            (1/home_odds) + (1/away_odds),  # market_efficiency
            home_recent['win_pct'],  # home_recent_win_pct
            away_recent['win_pct'],  # away_recent_win_pct
            home_recent['win_pct'] - away_recent['win_pct'],  # win_pct_diff
            home_recent['avg_score'],  # home_avg_score
            away_recent['avg_score'],  # away_avg_score
            home_recent['avg_score'] - away_recent['avg_score']  # scoring_diff
        ]])
        
        # Scale features
        features_scaled = self.scalers[model_key].transform(features)
        
        # Make prediction
        model = self.models[model_key]
        prediction = model.predict(features_scaled)[0]
        
        if hasattr(model, 'predict_proba'):
            probabilities = model.predict_proba(features_scaled)[0]
            home_win_prob = probabilities[1] if len(probabilities) > 1 else prediction
        else:
            home_win_prob = prediction
        
        return {
            'home_team': home_team,
            'away_team': away_team,
            'home_win_probability': float(home_win_prob),
            'away_win_probability': float(1 - home_win_prob),
            'predicted_winner': home_team if home_win_prob > 0.5 else away_team,
            'confidence': float(abs(home_win_prob - 0.5) * 2),
            'home_odds': float(home_odds),
            'away_odds': float(away_odds)
        }
